// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// NutritionAdviceDao is the data access object for the table nutrition_advice.
type NutritionAdviceDao struct {
	table    string                 // table is the underlying table name of the DAO.
	group    string                 // group is the database configuration group name of the current DAO.
	columns  NutritionAdviceColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler     // handlers for customized model modification.
}

// NutritionAdviceColumns defines and stores column names for the table nutrition_advice.
type NutritionAdviceColumns struct {
	Id            string //
	Type          string // 建议类型: warning, info, danger, success
	Title         string // 建议标题
	Description   string // 建议详情
	ConditionType string // 条件类型: protein, carbs, fat, calorie
	MinPercentage string // 最小百分比阈值
	MaxPercentage string // 最大百分比阈值
	IsDefault     string // 是否为默认建议
	Priority      string // 优先级，数字越大优先级越高
	Status        string // 状态：1-启用，0-禁用
	CreatedAt     string // 创建时间
	UpdatedAt     string // 更新时间
}

// nutritionAdviceColumns holds the columns for the table nutrition_advice.
var nutritionAdviceColumns = NutritionAdviceColumns{
	Id:            "id",
	Type:          "type",
	Title:         "title",
	Description:   "description",
	ConditionType: "condition_type",
	MinPercentage: "min_percentage",
	MaxPercentage: "max_percentage",
	IsDefault:     "is_default",
	Priority:      "priority",
	Status:        "status",
	CreatedAt:     "created_at",
	UpdatedAt:     "updated_at",
}

// NewNutritionAdviceDao creates and returns a new DAO object for table data access.
func NewNutritionAdviceDao(handlers ...gdb.ModelHandler) *NutritionAdviceDao {
	return &NutritionAdviceDao{
		group:    "default",
		table:    "nutrition_advice",
		columns:  nutritionAdviceColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *NutritionAdviceDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *NutritionAdviceDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *NutritionAdviceDao) Columns() NutritionAdviceColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *NutritionAdviceDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *NutritionAdviceDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *NutritionAdviceDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
