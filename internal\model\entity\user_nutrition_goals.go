// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// UserNutritionGoals is the golang structure for table user_nutrition_goals.
type UserNutritionGoals struct {
	Id            int64       `json:"id"            orm:"id"              description:""` //
	UserId        int64       `json:"userId"        orm:"user_id"         description:""` //
	CalorieTarget int64       `json:"calorieTarget" orm:"calorie_target"  description:""` //
	WeightTarget  float64     `json:"weightTarget"  orm:"weight_target"   description:""` //
	ProteinTarget int64       `json:"proteinTarget" orm:"protein_target"  description:""` //
	CarbsTarget   int64       `json:"carbsTarget"   orm:"carbs_target"    description:""` //
	FatTarget     int64       `json:"fatTarget"     orm:"fat_target"      description:""` //
	IsVegetarian  int         `json:"isVegetarian"  orm:"is_vegetarian"   description:""` //
	IsLowCarb     int         `json:"isLowCarb"     orm:"is_low_carb"     description:""` //
	IsHighProtein int         `json:"isHighProtein" orm:"is_high_protein" description:""` //
	IsGlutenFree  int         `json:"isGlutenFree"  orm:"is_gluten_free"  description:""` //
	IsLowSodium   int         `json:"isLowSodium"   orm:"is_low_sodium"   description:""` //
	CreatedAt     *gtime.Time `json:"createdAt"     orm:"created_at"      description:""` //
	UpdatedAt     *gtime.Time `json:"updatedAt"     orm:"updated_at"      description:""` //
}
