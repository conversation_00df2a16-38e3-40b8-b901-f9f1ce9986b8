// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// DietRecordFoods is the golang structure for table diet_record_foods.
type DietRecordFoods struct {
	Id           int64       `json:"id"           orm:"id"             description:""`         //
	DietRecordId int64       `json:"dietRecordId" orm:"diet_record_id" description:"关联饮食记录ID"` // 关联饮食记录ID
	FoodId       int64       `json:"foodId"       orm:"food_id"        description:"食物ID"`     // 食物ID
	FoodName     string      `json:"foodName"     orm:"food_name"      description:"食物名称"`     // 食物名称
	Amount       float64     `json:"amount"       orm:"amount"         description:"食物数量"`     // 食物数量
	Unit         string      `json:"unit"         orm:"unit"           description:"计量单位"`     // 计量单位
	Calories     float64     `json:"calories"     orm:"calories"       description:"热量(千卡)"`   // 热量(千卡)
	Protein      float64     `json:"protein"      orm:"protein"        description:"蛋白质(g)"`   // 蛋白质(g)
	Fat          float64     `json:"fat"          orm:"fat"            description:"脂肪(g)"`    // 脂肪(g)
	Carbs        float64     `json:"carbs"        orm:"carbs"          description:"碳水化合物(g)"` // 碳水化合物(g)
	Grams        float64     `json:"grams"        orm:"grams"          description:"食物克数"`     // 食物克数
	CreatedAt    *gtime.Time `json:"createdAt"    orm:"created_at"     description:"创建时间"`     // 创建时间
}
