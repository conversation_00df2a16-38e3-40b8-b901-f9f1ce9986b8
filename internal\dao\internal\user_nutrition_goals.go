// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// UserNutritionGoalsDao is the data access object for the table user_nutrition_goals.
type UserNutritionGoalsDao struct {
	table    string                    // table is the underlying table name of the DAO.
	group    string                    // group is the database configuration group name of the current DAO.
	columns  UserNutritionGoalsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler        // handlers for customized model modification.
}

// UserNutritionGoalsColumns defines and stores column names for the table user_nutrition_goals.
type UserNutritionGoalsColumns struct {
	Id            string //
	UserId        string //
	CalorieTarget string //
	WeightTarget  string //
	ProteinTarget string //
	CarbsTarget   string //
	FatTarget     string //
	IsVegetarian  string //
	IsLowCarb     string //
	IsHighProtein string //
	IsGlutenFree  string //
	IsLowSodium   string //
	CreatedAt     string //
	UpdatedAt     string //
}

// userNutritionGoalsColumns holds the columns for the table user_nutrition_goals.
var userNutritionGoalsColumns = UserNutritionGoalsColumns{
	Id:            "id",
	UserId:        "user_id",
	CalorieTarget: "calorie_target",
	WeightTarget:  "weight_target",
	ProteinTarget: "protein_target",
	CarbsTarget:   "carbs_target",
	FatTarget:     "fat_target",
	IsVegetarian:  "is_vegetarian",
	IsLowCarb:     "is_low_carb",
	IsHighProtein: "is_high_protein",
	IsGlutenFree:  "is_gluten_free",
	IsLowSodium:   "is_low_sodium",
	CreatedAt:     "created_at",
	UpdatedAt:     "updated_at",
}

// NewUserNutritionGoalsDao creates and returns a new DAO object for table data access.
func NewUserNutritionGoalsDao(handlers ...gdb.ModelHandler) *UserNutritionGoalsDao {
	return &UserNutritionGoalsDao{
		group:    "default",
		table:    "user_nutrition_goals",
		columns:  userNutritionGoalsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *UserNutritionGoalsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *UserNutritionGoalsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *UserNutritionGoalsDao) Columns() UserNutritionGoalsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *UserNutritionGoalsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *UserNutritionGoalsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *UserNutritionGoalsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
