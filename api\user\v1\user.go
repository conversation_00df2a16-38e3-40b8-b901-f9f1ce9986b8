package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

// 用户创建请求
type UserCreateReq struct {
	g.Meta    `path:"/users" tags:"User" method:"post" summary:"创建用户"`
	Username  string `json:"username" v:"required|length:3,20" dc:"用户名"`
	Email     string `json:"email" v:"required|email" dc:"邮箱"`
	Password  string `json:"password" v:"required|min:6" dc:"密码"`
	Phone     string `json:"phone" dc:"手机号"`
	Role      string `json:"role" dc:"角色"`
	Status    int    `json:"status" dc:"状态"`
	AvatarUrl string `json:"avatarUrl" dc:"头像URL"`
	Openid    string `json:"openid" dc:"微信openid"`
}

type UserCreateRes struct {
	Id         int64  `json:"id" dc:"用户ID"`
	Username   string `json:"username" dc:"用户名"`
	Email      string `json:"email" dc:"邮箱"`
	Role       string `json:"role" dc:"角色"`
	Status     int    `json:"status" dc:"状态"`
	CreateTime string `json:"createTime" dc:"创建时间"`
	AvatarUrl  string `json:"avatarUrl" dc:"头像URL"`
}

// 用户更新请求
type UserUpdateReq struct {
	g.Meta    `path:"/users/{userId}" tags:"User" method:"put" summary:"更新用户信息"`
	UserId    int64  `json:"userId" v:"required" dc:"用户ID"`
	Username  string `json:"username" dc:"用户名"`
	Email     string `json:"email" v:"email" dc:"邮箱"`
	Phone     string `json:"phone" dc:"手机号"`
	Role      string `json:"role" dc:"角色"`
	Status    int    `json:"status" dc:"状态"`
	AvatarUrl string `json:"avatarUrl" dc:"头像URL"`
}

type UserUpdateRes struct {
	Id         int64  `json:"id" dc:"用户ID"`
	Username   string `json:"username" dc:"用户名"`
	Email      string `json:"email" dc:"邮箱"`
	Role       string `json:"role" dc:"角色"`
	Status     int    `json:"status" dc:"状态"`
	CreateTime string `json:"createTime" dc:"创建时间"`
	AvatarUrl  string `json:"avatarUrl" dc:"头像URL"`
}

// 用户查询请求
type UserQueryReq struct {
	g.Meta  `path:"/users/{userId}" tags:"User" method:"get" summary:"获取用户详情"`
	UserId  int64 `json:"userId" v:"required" dc:"用户ID"`
}

type UserQueryRes struct {
	Id         int64  `json:"id" dc:"用户ID"`
	Username   string `json:"username" dc:"用户名"`
	Email      string `json:"email" dc:"邮箱"`
	Role       string `json:"role" dc:"角色"`
	Status     int    `json:"status" dc:"状态"`
	CreateTime string `json:"createTime" dc:"创建时间"`
	AvatarUrl  string `json:"avatarUrl" dc:"头像URL"`
	Phone      string `json:"phone" dc:"手机号"`
}

// 用户列表查询请求
type UserListReq struct {
	g.Meta   `path:"/users" tags:"User" method:"get" summary:"获取用户列表"`
	Page     int    `json:"page" v:"min:1" dc:"页码" default:"1"`
	Size     int    `json:"size" v:"between:1,100" dc:"每页数量" default:"10"`
	Status   *int   `json:"status" dc:"状态筛选"`
	Keyword  string `json:"keyword" dc:"关键词搜索"`
	Role     string `json:"role" dc:"角色筛选"`
}

type UserListRes struct {
	List  []UserInfo `json:"list" dc:"用户列表"`
	Total int64      `json:"total" dc:"总数"`
	Page  int        `json:"page" dc:"当前页"`
	Size  int        `json:"size" dc:"每页数量"`
}

type UserInfo struct {
	Id         int64  `json:"id" dc:"用户ID"`
	Username   string `json:"username" dc:"用户名"`
	Email      string `json:"email" dc:"邮箱"`
	Role       string `json:"role" dc:"角色"`
	Status     int    `json:"status" dc:"状态"`
	CreateTime string `json:"createTime" dc:"创建时间"`
	AvatarUrl  string `json:"avatarUrl" dc:"头像URL"`
	Phone      string `json:"phone" dc:"手机号"`
}

// 用户状态更新请求
type UserStatusUpdateReq struct {
	g.Meta `path:"/users/{userId}/status" tags:"User" method:"put" summary:"更新用户状态"`
	UserId int64 `json:"userId" v:"required" dc:"用户ID"`
	Status int   `json:"status" v:"in:0,1" dc:"状态：0-禁用，1-启用"`
}

type UserStatusUpdateRes struct {
	Success bool   `json:"success" dc:"是否成功"`
	Message string `json:"message" dc:"结果消息"`
}

// 头像上传URL请求
type AvatarUploadUrlReq struct {
	g.Meta      `path:"/avatar/upload-url" tags:"User" method:"get" summary:"获取头像上传URL"`
	ContentType string `json:"contentType" v:"required" dc:"文件内容类型"`
}

type AvatarUploadUrlRes struct {
	UploadUrl string `json:"uploadUrl" dc:"上传预签名URL"`
	FileName  string `json:"fileName" dc:"生成的文件名"`
	AvatarUrl string `json:"avatarUrl" dc:"头像访问URL"`
}

// 头像下载URL请求
type AvatarDownloadUrlReq struct {
	g.Meta `path:"/avatar/download-url" tags:"User" method:"get" summary:"获取头像下载URL"`
	UserId *int64 `json:"userId" dc:"用户ID（管理员查询用）"`
}

type AvatarDownloadUrlRes struct {
	AvatarUrl string `json:"avatarUrl" dc:"头像访问URL"`
	FileName  string `json:"fileName" dc:"文件名"`
}

// 当前用户信息请求
type CurrentUserInfoReq struct {
	g.Meta `path:"/info" tags:"User" method:"get" summary:"获取当前用户信息"`
}

type CurrentUserInfoRes struct {
	Id         int64  `json:"id" dc:"用户ID"`
	Username   string `json:"username" dc:"用户名"`
	Email      string `json:"email" dc:"邮箱"`
	Role       string `json:"role" dc:"角色"`
	Status     int    `json:"status" dc:"状态"`
	CreateTime string `json:"createTime" dc:"创建时间"`
	AvatarUrl  string `json:"avatarUrl" dc:"头像URL"`
	Phone      string `json:"phone" dc:"手机号"`
}

// 当前用户信息更新请求
type CurrentUserUpdateReq struct {
	g.Meta    `path:"/update" tags:"User" method:"put" summary:"更新当前用户信息"`
	Username  string `json:"username" dc:"用户名"`
	Email     string `json:"email" v:"email" dc:"邮箱"`
	Phone     string `json:"phone" dc:"手机号"`
	AvatarUrl string `json:"avatarUrl" dc:"头像URL"`
}

type CurrentUserUpdateRes struct {
	Success bool   `json:"success" dc:"是否成功"`
	Message string `json:"message" dc:"结果消息"`
}
