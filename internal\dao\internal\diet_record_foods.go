// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// DietRecordFoodsDao is the data access object for the table diet_record_foods.
type DietRecordFoodsDao struct {
	table    string                 // table is the underlying table name of the DAO.
	group    string                 // group is the database configuration group name of the current DAO.
	columns  DietRecordFoodsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler     // handlers for customized model modification.
}

// DietRecordFoodsColumns defines and stores column names for the table diet_record_foods.
type DietRecordFoodsColumns struct {
	Id           string //
	DietRecordId string // 关联饮食记录ID
	FoodId       string // 食物ID
	FoodName     string // 食物名称
	Amount       string // 食物数量
	Unit         string // 计量单位
	Calories     string // 热量(千卡)
	Protein      string // 蛋白质(g)
	Fat          string // 脂肪(g)
	Carbs        string // 碳水化合物(g)
	Grams        string // 食物克数
	CreatedAt    string // 创建时间
}

// dietRecordFoodsColumns holds the columns for the table diet_record_foods.
var dietRecordFoodsColumns = DietRecordFoodsColumns{
	Id:           "id",
	DietRecordId: "diet_record_id",
	FoodId:       "food_id",
	FoodName:     "food_name",
	Amount:       "amount",
	Unit:         "unit",
	Calories:     "calories",
	Protein:      "protein",
	Fat:          "fat",
	Carbs:        "carbs",
	Grams:        "grams",
	CreatedAt:    "created_at",
}

// NewDietRecordFoodsDao creates and returns a new DAO object for table data access.
func NewDietRecordFoodsDao(handlers ...gdb.ModelHandler) *DietRecordFoodsDao {
	return &DietRecordFoodsDao{
		group:    "default",
		table:    "diet_record_foods",
		columns:  dietRecordFoodsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *DietRecordFoodsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *DietRecordFoodsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *DietRecordFoodsDao) Columns() DietRecordFoodsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *DietRecordFoodsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *DietRecordFoodsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *DietRecordFoodsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
