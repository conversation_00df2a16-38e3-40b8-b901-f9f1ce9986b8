// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package dashboard

import (
	"context"

	"shikeyinxiang-goframe/api/dashboard/v1"
)

type IDashboardV1 interface {
	DashboardStats(ctx context.Context, req *v1.DashboardStatsReq) (res *v1.DashboardStatsRes, err error)
	NutritionTrend(ctx context.Context, req *v1.NutritionTrendReq) (res *v1.NutritionTrendRes, err error)
	LatestDietRecords(ctx context.Context, req *v1.LatestDietRecordsReq) (res *v1.LatestDietRecordsRes, err error)
	DietRecordDetail(ctx context.Context, req *v1.DietRecordDetailReq) (res *v1.DietRecordDetailRes, err error)
	PopularFoods(ctx context.Context, req *v1.PopularFoodsReq) (res *v1.PopularFoodsRes, err error)
	UserStats(ctx context.Context, req *v1.UserStatsReq) (res *v1.UserStatsRes, err error)
	SystemHealth(ctx context.Context, req *v1.SystemHealthReq) (res *v1.SystemHealthRes, err error)
}
