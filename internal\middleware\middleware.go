package middleware

import "github.com/gogf/gf/v2/net/ghttp"

// sMiddleware 中间件管理器
type sMiddleware struct {
	Response *sResponse // 响应处理中间件
	Cors     *sCors     // CORS跨域中间件
}

// Middleware 中间件管理器实例
var Middleware = &sMiddleware{
	Response: &sResponse{},
	Cors:     &sCors{},
}

// RegisterMiddlewares 注册所有中间件到路由组
// 按照执行顺序注册中间件：CORS -> 响应处理 -> 其他业务中间件
func (s *sMiddleware) RegisterMiddlewares(group *ghttp.RouterGroup) {
	// 1. CORS中间件 - 最先执行，处理跨域请求
	group.Middleware(s.Cors.Handler)
	
	// 2. 响应处理中间件 - 统一处理响应格式和错误
	group.Middleware(s.Response.Handler)
	
	// 3. 未来可以在这里添加其他中间件
	// group.Middleware(s.Auth.Handler)      // 认证中间件
	// group.Middleware(s.RateLimit.Handler) // 限流中间件
	// group.Middleware(s.Logger.Handler)    // 日志中间件
}

// GetResponseMiddleware 获取响应处理中间件（兼容性方法）
func (s *sMiddleware) GetResponseMiddleware() ghttp.HandlerFunc {
	return s.Response.Handler
}

// GetCorsMiddleware 获取CORS中间件（兼容性方法）
func (s *sMiddleware) GetCorsMiddleware() ghttp.HandlerFunc {
	return s.Cors.Handler
}
