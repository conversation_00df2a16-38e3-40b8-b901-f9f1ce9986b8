// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// FoodCategory is the golang structure of table food_category for DAO operations like Where/Data.
type FoodCategory struct {
	g.Meta      `orm:"table:food_category, do:true"`
	Id          interface{} //
	Name        interface{} // 分类名称
	Description interface{} // 分类描述
	Color       interface{} // 分类颜色
	SortOrder   interface{} // 排序顺序
	CreatedAt   *gtime.Time // 创建时间
	UpdatedAt   *gtime.Time // 更新时间
}
