// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// User is the golang structure of table user for DAO operations like Where/Data.
type User struct {
	g.Meta     `orm:"table:user, do:true"`
	Id         interface{} //
	Username   interface{} //
	Password   interface{} //
	Email      interface{} //
	Role       interface{} //
	Status     interface{} // 1表示用户启动，0表示用户被封禁
	CreateTime *gtime.Time // 创建时间
	Openid     interface{} // 微信openid，用于唯一标识用户
	AvatarUrl  interface{} // 用户头像URL
}
