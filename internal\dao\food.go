// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"shikeyinxiang-goframe/internal/dao/internal"
)

// foodDao is the data access object for the table food.
// You can define custom methods on it to extend its functionality as needed.
type foodDao struct {
	*internal.FoodDao
}

var (
	// Food is a globally accessible object for table food operations.
	Food = foodDao{internal.NewFoodDao()}
)

// Add your custom methods and functionality below.
