// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// NutritionAdvice is the golang structure for table nutrition_advice.
type NutritionAdvice struct {
	Id            int64       `json:"id"            orm:"id"             description:""`                                     //
	Type          string      `json:"type"          orm:"type"           description:"建议类型: warning, info, danger, success"` // 建议类型: warning, info, danger, success
	Title         string      `json:"title"         orm:"title"          description:"建议标题"`                                 // 建议标题
	Description   string      `json:"description"   orm:"description"    description:"建议详情"`                                 // 建议详情
	ConditionType string      `json:"conditionType" orm:"condition_type" description:"条件类型: protein, carbs, fat, calorie"`   // 条件类型: protein, carbs, fat, calorie
	MinPercentage int         `json:"minPercentage" orm:"min_percentage" description:"最小百分比阈值"`                              // 最小百分比阈值
	MaxPercentage int         `json:"maxPercentage" orm:"max_percentage" description:"最大百分比阈值"`                              // 最大百分比阈值
	IsDefault     int         `json:"isDefault"     orm:"is_default"     description:"是否为默认建议"`                              // 是否为默认建议
	Priority      int         `json:"priority"      orm:"priority"       description:"优先级，数字越大优先级越高"`                        // 优先级，数字越大优先级越高
	Status        int         `json:"status"        orm:"status"         description:"状态：1-启用，0-禁用"`                         // 状态：1-启用，0-禁用
	CreatedAt     *gtime.Time `json:"createdAt"     orm:"created_at"     description:"创建时间"`                                 // 创建时间
	UpdatedAt     *gtime.Time `json:"updatedAt"     orm:"updated_at"     description:"更新时间"`                                 // 更新时间
}
