// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package diet

import (
	"context"

	"shikeyinxiang-goframe/api/diet/v1"
)

type IDietV1 interface {
	DietRecordAdd(ctx context.Context, req *v1.DietRecordAddReq) (res *v1.DietRecordAddRes, err error)
	DietRecordList(ctx context.Context, req *v1.DietRecordListReq) (res *v1.DietRecordListRes, err error)
	DietRecordDetail(ctx context.Context, req *v1.DietRecordDetailReq) (res *v1.DietRecordDetailRes, err error)
	DietRecordUpdate(ctx context.Context, req *v1.DietRecordUpdateReq) (res *v1.DietRecordUpdateRes, err error)
	DietRecordDelete(ctx context.Context, req *v1.DietRecordDeleteReq) (res *v1.DietRecordDeleteRes, err error)
	DietRecordStats(ctx context.Context, req *v1.DietRecordStatsReq) (res *v1.DietRecordStatsRes, err error)
	PopularFoods(ctx context.Context, req *v1.PopularFoodsReq) (res *v1.PopularFoodsRes, err error)
	ActiveUsers(ctx context.Context, req *v1.ActiveUsersReq) (res *v1.ActiveUsersRes, err error)
}
