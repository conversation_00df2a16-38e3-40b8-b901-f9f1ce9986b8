package auth

import "github.com/gogf/gf/v2/errors/gcode"

// 认证模块错误码定义
// 错误码格式：AABBBCCC
// AA: 10 (认证模块)
// BBB: 001-999 (功能模块)
// CCC: 001-999 (具体错误)

var (
	// 登录相关错误 (10001xxx)
	CodeAuthInvalidCredentials = gcode.New(********, "邮箱或密码错误", nil)
	CodeAuthUserNotFound       = gcode.New(********, "用户不存在", nil)
	CodeAuthAccountDisabled    = gcode.New(********, "账号已被封禁", nil)
	CodeAuthInvalidRole        = gcode.New(********, "账号角色不匹配", nil)

	// Token相关错误 (10002xxx)
	CodeAuthTokenInvalid = gcode.New(********, "无效的令牌", nil)
	CodeAuthTokenExpired = gcode.New(********, "令牌已过期", nil)

	// 微信登录相关错误 (10003xxx)
	CodeAuthWechatCodeInvalid = gcode.New(********, "微信登录码无效", nil)
	CodeAuthWechatApiError    = gcode.New(********, "微信API调用失败", nil)

	// 密码相关错误 (10004xxx)
	CodeAuthPasswordWeak     = gcode.New(********, "密码强度不够", nil)
	CodeAuthOldPasswordWrong = gcode.New(********, "原密码错误", nil)
)
