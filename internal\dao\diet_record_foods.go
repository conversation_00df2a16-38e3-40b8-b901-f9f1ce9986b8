// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"shikeyinxiang-goframe/internal/dao/internal"
)

// dietRecordFoodsDao is the data access object for the table diet_record_foods.
// You can define custom methods on it to extend its functionality as needed.
type dietRecordFoodsDao struct {
	*internal.DietRecordFoodsDao
}

var (
	// DietRecordFoods is a globally accessible object for table diet_record_foods operations.
	DietRecordFoods = dietRecordFoodsDao{internal.NewDietRecordFoodsDao()}
)

// Add your custom methods and functionality below.
