// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// DietRecords is the golang structure of table diet_records for DAO operations like Where/Data.
type DietRecords struct {
	g.Meta       `orm:"table:diet_records, do:true"`
	Id           interface{} //
	UserId       interface{} // 用户ID
	Date         *gtime.Time // 记录日期
	Time         *gtime.Time // 记录时间
	MealType     interface{} // 餐次类型: breakfast/lunch/dinner/snacks
	Remark       interface{} // 备注信息
	TotalCalorie interface{} // 总热量(千卡)
	CreatedAt    *gtime.Time // 创建时间
	UpdatedAt    *gtime.Time // 更新时间
}
