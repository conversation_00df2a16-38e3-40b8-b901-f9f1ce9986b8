// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"shikeyinxiang-goframe/internal/dao/internal"
)

// foodCategoryDao is the data access object for the table food_category.
// You can define custom methods on it to extend its functionality as needed.
type foodCategoryDao struct {
	*internal.FoodCategoryDao
}

var (
	// FoodCategory is a globally accessible object for table food_category operations.
	FoodCategory = foodCategoryDao{internal.NewFoodCategoryDao()}
)

// Add your custom methods and functionality below.
