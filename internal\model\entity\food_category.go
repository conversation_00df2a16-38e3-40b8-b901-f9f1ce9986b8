// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// FoodCategory is the golang structure for table food_category.
type FoodCategory struct {
	Id          int         `json:"id"          orm:"id"          description:""`     //
	Name        string      `json:"name"        orm:"name"        description:"分类名称"` // 分类名称
	Description string      `json:"description" orm:"description" description:"分类描述"` // 分类描述
	Color       string      `json:"color"       orm:"color"       description:"分类颜色"` // 分类颜色
	SortOrder   int64       `json:"sortOrder"   orm:"sort_order"  description:"排序顺序"` // 排序顺序
	CreatedAt   *gtime.Time `json:"createdAt"   orm:"created_at"  description:"创建时间"` // 创建时间
	UpdatedAt   *gtime.Time `json:"updatedAt"   orm:"updated_at"  description:"更新时间"` // 更新时间
}
