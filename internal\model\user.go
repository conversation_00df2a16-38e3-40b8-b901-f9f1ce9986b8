package model

// UserCreateInput 用户创建输入参数
type UserCreateInput struct {
	Username  string `json:"username" v:"required|length:3,20" dc:"用户名"`
	Email     string `json:"email" v:"required|email" dc:"邮箱"`
	Password  string `json:"password" dc:"密码"`
	Phone     string `json:"phone" dc:"手机号"`
	Role      string `json:"role" dc:"角色"`
	Status    int    `json:"status" dc:"状态"`
	AvatarUrl string `json:"avatarUrl" dc:"头像URL"`
	Openid    string `json:"openid" dc:"微信OpenID"`
}

// UserCreateOutput 用户创建输出结果
type UserCreateOutput struct {
	UserInfo *UserInfo `json:"userInfo" dc:"用户信息"`
}

// UserUpdateInput 用户更新输入参数
type UserUpdateInput struct {
	Id        int64  `json:"id" v:"required" dc:"用户ID"`
	Username  string `json:"username" dc:"用户名"`
	Email     string `json:"email" dc:"邮箱"`
	Role      string `json:"role" dc:"角色"`
	Status    int    `json:"status" dc:"状态"`
	AvatarUrl string `json:"avatarUrl" dc:"头像URL"`
	Password  string `json:"password" dc:"密码"`
}

// UserUpdateOutput 用户更新输出结果
type UserUpdateOutput struct {
	Success bool   `json:"success" dc:"是否成功"`
	Message string `json:"message" dc:"结果消息"`
}

// UserStatusUpdateInput 用户状态更新输入参数
type UserStatusUpdateInput struct {
	UserId int64 `json:"userId" v:"required" dc:"用户ID"`
	Status int   `json:"status" v:"required|in:0,1" dc:"状态：0-禁用，1-启用"`
}

// UserStatusUpdateOutput 用户状态更新输出结果
type UserStatusUpdateOutput struct {
	Success bool   `json:"success" dc:"是否成功"`
	Message string `json:"message" dc:"结果消息"`
}

// UserQueryInput 用户查询输入参数
type UserQueryInput struct {
	Page    int    `json:"page" v:"required|min:1" dc:"页码"`
	Size    int    `json:"size" v:"required|min:1|max:100" dc:"每页数量"`
	Status  *int   `json:"status" dc:"状态筛选"`
	Keyword string `json:"keyword" dc:"关键词搜索"`
	Role    string `json:"role" dc:"角色筛选"`
}

// UserListOutput 用户列表输出结果
type UserListOutput struct {
	List  []*UserInfo `json:"list" dc:"用户列表"`
	Total int64       `json:"total" dc:"总数"`
	Page  int         `json:"page" dc:"当前页"`
	Size  int         `json:"size" dc:"每页数量"`
}

// PasswordVerifyInput 密码验证输入参数
type PasswordVerifyInput struct {
	UsernameOrEmail string `json:"usernameOrEmail" v:"required" dc:"用户名或邮箱"`
	Password        string `json:"password" v:"required" dc:"密码"`
}

// PasswordVerifyOutput 密码验证输出结果
type PasswordVerifyOutput struct {
	Valid   bool   `json:"valid" dc:"是否验证通过"`
	Message string `json:"message" dc:"验证结果消息"`
}

// UserAvatarUpdateInput 用户头像更新输入参数
type UserAvatarUpdateInput struct {
	UserId    int64  `json:"userId" v:"required" dc:"用户ID"`
	AvatarUrl string `json:"avatarUrl" v:"required" dc:"头像URL"`
}

// UserAvatarUpdateOutput 用户头像更新输出结果
type UserAvatarUpdateOutput struct {
	Success bool   `json:"success" dc:"是否成功"`
	Message string `json:"message" dc:"结果消息"`
}

// AvatarUploadUrlInput 头像上传URL生成输入参数
type AvatarUploadUrlInput struct {
	UserId      int64  `json:"userId" v:"required" dc:"用户ID"`
	ContentType string `json:"contentType" v:"required" dc:"文件类型"`
}

// AvatarUploadUrlOutput 头像上传URL生成输出结果
type AvatarUploadUrlOutput struct {
	UploadUrl string `json:"uploadUrl" dc:"上传URL"`
	FileName  string `json:"fileName" dc:"文件名"`
	AvatarUrl string `json:"avatarUrl" dc:"头像访问URL"`
}
