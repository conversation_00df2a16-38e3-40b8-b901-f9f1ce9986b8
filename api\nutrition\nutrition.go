// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package nutrition

import (
	"context"

	"shikeyinxiang-goframe/api/nutrition/v1"
)

type INutritionV1 interface {
	DailyNutrition(ctx context.Context, req *v1.DailyNutritionReq) (res *v1.DailyNutritionRes, err error)
	NutritionTrend(ctx context.Context, req *v1.NutritionTrendReq) (res *v1.NutritionTrendRes, err error)
	NutritionDetails(ctx context.Context, req *v1.NutritionDetailsReq) (res *v1.NutritionDetailsRes, err error)
	NutritionAdvice(ctx context.Context, req *v1.NutritionAdviceReq) (res *v1.NutritionAdviceRes, err error)
	HealthReport(ctx context.Context, req *v1.HealthReportReq) (res *v1.HealthReportRes, err error)
	ComplianceRate(ctx context.Context, req *v1.ComplianceRateReq) (res *v1.ComplianceRateRes, err error)
	NutritionGoal(ctx context.Context, req *v1.NutritionGoalReq) (res *v1.NutritionGoalRes, err error)
	NutritionGoalSet(ctx context.Context, req *v1.NutritionGoalSetReq) (res *v1.NutritionGoalSetRes, err error)
	NutritionSummary(ctx context.Context, req *v1.NutritionSummaryReq) (res *v1.NutritionSummaryRes, err error)
}
