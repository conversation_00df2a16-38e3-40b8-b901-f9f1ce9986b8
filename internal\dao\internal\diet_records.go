// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// DietRecordsDao is the data access object for the table diet_records.
type DietRecordsDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  DietRecordsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// DietRecordsColumns defines and stores column names for the table diet_records.
type DietRecordsColumns struct {
	Id           string //
	UserId       string // 用户ID
	Date         string // 记录日期
	Time         string // 记录时间
	MealType     string // 餐次类型: breakfast/lunch/dinner/snacks
	Remark       string // 备注信息
	TotalCalorie string // 总热量(千卡)
	CreatedAt    string // 创建时间
	UpdatedAt    string // 更新时间
}

// dietRecordsColumns holds the columns for the table diet_records.
var dietRecordsColumns = DietRecordsColumns{
	Id:           "id",
	UserId:       "user_id",
	Date:         "date",
	Time:         "time",
	MealType:     "meal_type",
	Remark:       "remark",
	TotalCalorie: "total_calorie",
	CreatedAt:    "created_at",
	UpdatedAt:    "updated_at",
}

// NewDietRecordsDao creates and returns a new DAO object for table data access.
func NewDietRecordsDao(handlers ...gdb.ModelHandler) *DietRecordsDao {
	return &DietRecordsDao{
		group:    "default",
		table:    "diet_records",
		columns:  dietRecordsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *DietRecordsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *DietRecordsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *DietRecordsDao) Columns() DietRecordsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *DietRecordsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *DietRecordsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *DietRecordsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
