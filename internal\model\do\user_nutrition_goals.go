// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// UserNutritionGoals is the golang structure of table user_nutrition_goals for DAO operations like Where/Data.
type UserNutritionGoals struct {
	g.Meta        `orm:"table:user_nutrition_goals, do:true"`
	Id            interface{} //
	UserId        interface{} //
	CalorieTarget interface{} //
	WeightTarget  interface{} //
	ProteinTarget interface{} //
	CarbsTarget   interface{} //
	FatTarget     interface{} //
	IsVegetarian  interface{} //
	IsLowCarb     interface{} //
	IsHighProtein interface{} //
	IsGlutenFree  interface{} //
	IsLowSodium   interface{} //
	CreatedAt     *gtime.Time //
	UpdatedAt     *gtime.Time //
}
