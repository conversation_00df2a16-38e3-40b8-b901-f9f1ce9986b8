// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// User is the golang structure for table user.
type User struct {
	Id         int64       `json:"id"         orm:"id"          description:""`                  //
	Username   string      `json:"username"   orm:"username"    description:""`                  //
	Password   string      `json:"password"   orm:"password"    description:""`                  //
	Email      string      `json:"email"      orm:"email"       description:""`                  //
	Role       string      `json:"role"       orm:"role"        description:""`                  //
	Status     int         `json:"status"     orm:"status"      description:"1表示用户启动，0表示用户被封禁"`  // 1表示用户启动，0表示用户被封禁
	CreateTime *gtime.Time `json:"createTime" orm:"create_time" description:"创建时间"`              // 创建时间
	Openid     string      `json:"openid"     orm:"openid"      description:"微信openid，用于唯一标识用户"` // 微信openid，用于唯一标识用户
	AvatarUrl  string      `json:"avatarUrl"  orm:"avatar_url"  description:"用户头像URL"`           // 用户头像URL
}
