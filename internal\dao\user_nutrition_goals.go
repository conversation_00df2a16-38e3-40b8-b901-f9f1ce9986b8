// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"shikeyinxiang-goframe/internal/dao/internal"
)

// userNutritionGoalsDao is the data access object for the table user_nutrition_goals.
// You can define custom methods on it to extend its functionality as needed.
type userNutritionGoalsDao struct {
	*internal.UserNutritionGoalsDao
}

var (
	// UserNutritionGoals is a globally accessible object for table user_nutrition_goals operations.
	UserNutritionGoals = userNutritionGoalsDao{internal.NewUserNutritionGoalsDao()}
)

// Add your custom methods and functionality below.
