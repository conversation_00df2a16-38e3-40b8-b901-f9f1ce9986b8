package user

import "github.com/gogf/gf/v2/errors/gcode"

// 用户模块错误码定义
// 错误码格式：AABBBCCC
// AA: 20 (用户模块)
// BBB: 001-999 (功能模块)
// CCC: 001-999 (具体错误)

var (
	// 用户基础操作错误 (20001xxx)
	CodeUserNotFound       = gcode.New(20001001, "用户不存在", nil)
	CodeUserAlreadyExists  = gcode.New(20001002, "用户已存在", nil)
	CodeUserEmailExists    = gcode.New(20001003, "邮箱已被使用", nil)
	CodeUserUsernameExists = gcode.New(20001004, "用户名已被使用", nil)

	// 用户数据验证错误 (20002xxx)
	CodeUserInvalidEmail    = gcode.New(20002001, "邮箱格式无效", nil)
	CodeUserInvalidPhone    = gcode.New(20002002, "手机号格式无效", nil)
	CodeUserPasswordTooWeak = gcode.New(20002003, "密码强度不够", nil)

	// 用户状态相关错误 (20003xxx)
	CodeUserStatusInvalid = gcode.New(20003001, "用户状态无效", nil)

	// 用户头像相关错误 (20004xxx)
	CodeUserAvatarUploadFailed = gcode.New(20004001, "头像上传失败", nil)

	// 用户权限相关错误 (20005xxx)
	CodeUserPermissionDenied = gcode.New(20005001, "权限不足", nil)
)
