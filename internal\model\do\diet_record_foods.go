// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// DietRecordFoods is the golang structure of table diet_record_foods for DAO operations like Where/Data.
type DietRecordFoods struct {
	g.Meta       `orm:"table:diet_record_foods, do:true"`
	Id           interface{} //
	DietRecordId interface{} // 关联饮食记录ID
	FoodId       interface{} // 食物ID
	FoodName     interface{} // 食物名称
	Amount       interface{} // 食物数量
	Unit         interface{} // 计量单位
	Calories     interface{} // 热量(千卡)
	Protein      interface{} // 蛋白质(g)
	Fat          interface{} // 脂肪(g)
	Carbs        interface{} // 碳水化合物(g)
	Grams        interface{} // 食物克数
	CreatedAt    *gtime.Time // 创建时间
}
