package middleware

import "github.com/gogf/gf/v2/net/ghttp"

// sCors CORS跨域处理中间件
type sCors struct{}

// Handler CORS跨域处理中间件
// 处理跨域请求，允许前端应用访问API
func (s *sCors) Handler(r *ghttp.Request) {
	// 使用GoFrame默认的CORS处理
	r.Response.CORSDefault()
	
	// 继续执行后续中间件
	r.Middleware.Next()
}

// HandlerWithCustom 自定义CORS处理中间件（可选）
// 如果需要更精细的CORS控制，可以使用此方法
func (s *sCors) HandlerWithCustom(r *ghttp.Request) {
	// 设置自定义CORS头
	r.Response.Header().Set("Access-Control-Allow-Origin", "*")
	r.Response.Header().Set("Access-Control-Allow-Methods", "GET,POST,PUT,DELETE,OPTIONS")
	r.Response.Header().Set("Access-Control-Allow-Headers", "Content-Type,Authorization,X-Requested-With")
	r.Response.Header().Set("Access-Control-Max-Age", "86400")
	
	// 处理预检请求
	if r.Method == "OPTIONS" {
		r.Response.WriteHeader(200)
		return
	}
	
	// 继续执行后续中间件
	r.Middleware.Next()
}
