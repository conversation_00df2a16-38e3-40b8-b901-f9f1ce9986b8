// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// FoodDao is the data access object for the table food.
type FoodDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  FoodColumns        // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// FoodColumns defines and stores column names for the table food.
type FoodColumns struct {
	Id         string //
	FoodName   string //
	Measure    string //
	Grams      string // 克数。约定：0.01 代表微量。
	Calories   string // 卡路里。约定：0.01 代表微量。
	Protein    string // 蛋白质含量(克)。约定：0.01 代表微量。
	Fat        string // 总脂肪含量(克)。约定：0.01 代表微量。
	SatFat     string // 饱和脂肪含量(克)。约定：0.01 代表微量。
	Fiber      string //
	Carbs      string // 碳水化合物含量(克)。约定：0.01 代表微量。
	ImageUrl   string // 食物图片URL
	CategoryId string // 分类ID
}

// foodColumns holds the columns for the table food.
var foodColumns = FoodColumns{
	Id:         "id",
	FoodName:   "food_name",
	Measure:    "measure",
	Grams:      "grams",
	Calories:   "calories",
	Protein:    "protein",
	Fat:        "fat",
	SatFat:     "sat_fat",
	Fiber:      "fiber",
	Carbs:      "carbs",
	ImageUrl:   "image_url",
	CategoryId: "category_id",
}

// NewFoodDao creates and returns a new DAO object for table data access.
func NewFoodDao(handlers ...gdb.ModelHandler) *FoodDao {
	return &FoodDao{
		group:    "default",
		table:    "food",
		columns:  foodColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *FoodDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *FoodDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *FoodDao) Columns() FoodColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *FoodDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *FoodDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *FoodDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
