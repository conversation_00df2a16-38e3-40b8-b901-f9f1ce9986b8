// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// Food is the golang structure for table food.
type Food struct {
	Id         int     `json:"id"         orm:"id"          description:""`                         //
	FoodName   string  `json:"foodName"   orm:"food_name"   description:""`                         //
	Measure    string  `json:"measure"    orm:"measure"     description:""`                         //
	Grams      float64 `json:"grams"      orm:"grams"       description:"克数。约定：0.01 代表微量。"`         // 克数。约定：0.01 代表微量。
	Calories   float64 `json:"calories"   orm:"calories"    description:"卡路里。约定：0.01 代表微量。"`        // 卡路里。约定：0.01 代表微量。
	Protein    float64 `json:"protein"    orm:"protein"     description:"蛋白质含量(克)。约定：0.01 代表微量。"`   // 蛋白质含量(克)。约定：0.01 代表微量。
	Fat        float64 `json:"fat"        orm:"fat"         description:"总脂肪含量(克)。约定：0.01 代表微量。"`   // 总脂肪含量(克)。约定：0.01 代表微量。
	SatFat     float64 `json:"satFat"     orm:"sat_fat"     description:"饱和脂肪含量(克)。约定：0.01 代表微量。"`  // 饱和脂肪含量(克)。约定：0.01 代表微量。
	Fiber      string  `json:"fiber"      orm:"fiber"       description:""`                         //
	Carbs      float64 `json:"carbs"      orm:"carbs"       description:"碳水化合物含量(克)。约定：0.01 代表微量。"` // 碳水化合物含量(克)。约定：0.01 代表微量。
	ImageUrl   string  `json:"imageUrl"   orm:"image_url"   description:"食物图片URL"`                  // 食物图片URL
	CategoryId int     `json:"categoryId" orm:"category_id" description:"分类ID"`                     // 分类ID
}
