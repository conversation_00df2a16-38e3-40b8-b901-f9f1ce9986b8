// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// NutritionAdvice is the golang structure of table nutrition_advice for DAO operations like Where/Data.
type NutritionAdvice struct {
	g.Meta        `orm:"table:nutrition_advice, do:true"`
	Id            interface{} //
	Type          interface{} // 建议类型: warning, info, danger, success
	Title         interface{} // 建议标题
	Description   interface{} // 建议详情
	ConditionType interface{} // 条件类型: protein, carbs, fat, calorie
	MinPercentage interface{} // 最小百分比阈值
	MaxPercentage interface{} // 最大百分比阈值
	IsDefault     interface{} // 是否为默认建议
	Priority      interface{} // 优先级，数字越大优先级越高
	Status        interface{} // 状态：1-启用，0-禁用
	CreatedAt     *gtime.Time // 创建时间
	UpdatedAt     *gtime.Time // 更新时间
}
