// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"shikeyinxiang-goframe/internal/dao/internal"
)

// dietRecordsDao is the data access object for the table diet_records.
// You can define custom methods on it to extend its functionality as needed.
type dietRecordsDao struct {
	*internal.DietRecordsDao
}

var (
	// DietRecords is a globally accessible object for table diet_records operations.
	DietRecords = dietRecordsDao{internal.NewDietRecordsDao()}
)

// Add your custom methods and functionality below.
