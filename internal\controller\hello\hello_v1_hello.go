package hello

import (
	"context"
	"github.com/gogf/gf/v2/errors/gerror"

	"shikeyinxiang-goframe/api/hello/v1"
	"shikeyinxiang-goframe/internal/logic/errcode"
)

func (c *ControllerV1) Hello(ctx context.Context, req *v1.HelloReq) (res *v1.HelloRes, err error) {
	// 测试成功响应 - 返回数据，中间件会处理成功格式
	return &v1.HelloRes{}, nil

	// 测试错误响应 - 取消注释下面的代码来测试错误处理
	return nil, gerror.NewCode(errcode.CodeInvalidParam, "测试错误处理")
}
