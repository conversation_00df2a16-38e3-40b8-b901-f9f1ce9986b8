// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"shikeyinxiang-goframe/internal/dao/internal"
)

// nutritionAdviceDao is the data access object for the table nutrition_advice.
// You can define custom methods on it to extend its functionality as needed.
type nutritionAdviceDao struct {
	*internal.NutritionAdviceDao
}

var (
	// NutritionAdvice is a globally accessible object for table nutrition_advice operations.
	NutritionAdvice = nutritionAdviceDao{internal.NewNutritionAdviceDao()}
)

// Add your custom methods and functionality below.
