// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// FoodCategoryDao is the data access object for the table food_category.
type FoodCategoryDao struct {
	table    string              // table is the underlying table name of the DAO.
	group    string              // group is the database configuration group name of the current DAO.
	columns  FoodCategoryColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler  // handlers for customized model modification.
}

// FoodCategoryColumns defines and stores column names for the table food_category.
type FoodCategoryColumns struct {
	Id          string //
	Name        string // 分类名称
	Description string // 分类描述
	Color       string // 分类颜色
	SortOrder   string // 排序顺序
	CreatedAt   string // 创建时间
	UpdatedAt   string // 更新时间
}

// foodCategoryColumns holds the columns for the table food_category.
var foodCategoryColumns = FoodCategoryColumns{
	Id:          "id",
	Name:        "name",
	Description: "description",
	Color:       "color",
	SortOrder:   "sort_order",
	CreatedAt:   "created_at",
	UpdatedAt:   "updated_at",
}

// NewFoodCategoryDao creates and returns a new DAO object for table data access.
func NewFoodCategoryDao(handlers ...gdb.ModelHandler) *FoodCategoryDao {
	return &FoodCategoryDao{
		group:    "default",
		table:    "food_category",
		columns:  foodCategoryColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *FoodCategoryDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *FoodCategoryDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *FoodCategoryDao) Columns() FoodCategoryColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *FoodCategoryDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *FoodCategoryDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *FoodCategoryDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
