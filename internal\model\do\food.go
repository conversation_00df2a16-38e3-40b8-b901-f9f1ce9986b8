// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// Food is the golang structure of table food for DAO operations like Where/Data.
type Food struct {
	g.Meta     `orm:"table:food, do:true"`
	Id         interface{} //
	FoodName   interface{} //
	Measure    interface{} //
	Grams      interface{} // 克数。约定：0.01 代表微量。
	Calories   interface{} // 卡路里。约定：0.01 代表微量。
	Protein    interface{} // 蛋白质含量(克)。约定：0.01 代表微量。
	Fat        interface{} // 总脂肪含量(克)。约定：0.01 代表微量。
	SatFat     interface{} // 饱和脂肪含量(克)。约定：0.01 代表微量。
	Fiber      interface{} //
	Carbs      interface{} // 碳水化合物含量(克)。约定：0.01 代表微量。
	ImageUrl   interface{} // 食物图片URL
	CategoryId interface{} // 分类ID
}
