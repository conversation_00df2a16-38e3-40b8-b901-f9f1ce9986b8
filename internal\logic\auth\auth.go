package auth

import (
	"context"
	"crypto/rand"
	"encoding/json"
	"fmt"
	"math/big"
	"strings"
	"time"

	"github.com/gogf/gf/v2/crypto/gmd5"
	"github.com/gogf/gf/v2/database/gredis"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/gclient"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/golang-jwt/jwt/v5"
	"golang.org/x/crypto/bcrypt"

	"shikeyinxiang-goframe/internal/model"
	"shikeyinxiang-goframe/internal/service"
)

type sAuth struct{}

func init() {
	service.RegisterAuth(New())
}

func New() service.IAuth {
	return &sAuth{}
}

// UserLogin 用户登录
func (s *sAuth) UserLogin(ctx context.Context, in *model.UserLoginInput) (*model.UserLoginOutput, error) {
	// 1. 参数验证
	if in.Email == "" {
		return nil, gerror.NewCode(CodeAuthInvalidCredentials, "邮箱不能为空")
	}

	// 2. 查询用户
	userInfo, err := service.User().GetUserByEmail(ctx, in.Email)
	if err != nil {
		return nil, err
	}
	if userInfo == nil {
		return nil, gerror.NewCode(CodeAuthInvalidCredentials, "邮箱或密码错误")
	}

	// 3. 验证密码
	if !s.verifyPassword(in.Password, userInfo.Password) {
		return nil, gerror.NewCode(CodeAuthInvalidCredentials, "邮箱或密码错误")
	}

	// 4. 验证角色
	if userInfo.Role != "USER" {
		return nil, gerror.NewCode(CodeAuthInvalidRole, "该账号不是普通用户账号")
	}

	// 5. 检查用户状态
	if userInfo.Status == 0 {
		return nil, gerror.NewCode(CodeAuthAccountDisabled, "账号已被封禁，请联系管理员")
	}

	// 6. 生成JWT token
	token, err := s.generateToken(ctx, userInfo)
	if err != nil {
		return nil, err
	}

	return &model.UserLoginOutput{
		Token:    token,
		UserInfo: userInfo,
	}, nil
}

// AdminLogin 管理员登录
func (s *sAuth) AdminLogin(ctx context.Context, in *model.AdminLoginInput) (*model.AdminLoginOutput, error) {
	// 1. 参数验证
	if in.Username == "" {
		return nil, gerror.NewCode(CodeAuthInvalidCredentials, "用户名不能为空")
	}

	// 2. 查询用户
	userInfo, err := service.User().GetUserByUsername(ctx, in.Username)
	if err != nil {
		return nil, err
	}
	if userInfo == nil {
		return nil, gerror.NewCode(CodeAuthInvalidCredentials, "用户名或密码错误")
	}

	// 3. 验证密码
	if !s.verifyPassword(in.Password, userInfo.Password) {
		return nil, gerror.NewCode(CodeAuthInvalidCredentials, "用户名或密码错误")
	}

	// 4. 验证角色
	if userInfo.Role != "ADMIN" {
		return nil, gerror.NewCode(CodeAuthInvalidRole, "该账号不是管理员账号")
	}

	// 5. 检查用户状态
	if userInfo.Status == 0 {
		return nil, gerror.NewCode(CodeAuthAccountDisabled, "账号已被封禁，请联系管理员")
	}

	// 6. 生成JWT token
	token, err := s.generateToken(ctx, userInfo)
	if err != nil {
		return nil, err
	}

	return &model.AdminLoginOutput{
		Token:    token,
		UserInfo: userInfo,
	}, nil
}

// WechatLogin 微信登录
func (s *sAuth) WechatLogin(ctx context.Context, in *model.WechatLoginInput) (*model.WechatLoginOutput, error) {
	// 1. 参数验证
	if in.Code == "" {
		return nil, gerror.NewCode(CodeAuthWechatCodeInvalid, "微信登录码不能为空")
	}

	// 2. 调用微信API获取openid
	openid, err := s.getWechatOpenid(ctx, in.Code)
	if err != nil {
		return nil, gerror.NewCode(CodeAuthWechatApiError, "微信API调用失败: "+err.Error())
	}

	// 3. 通过openid查询用户
	userInfo, err := service.User().GetUserByOpenid(ctx, openid)
	if err != nil {
		return nil, err
	}

	// 4. 如果用户不存在，创建新用户
	if userInfo == nil {
		userInfo, err = s.createWechatUser(ctx, openid)
		if err != nil {
			return nil, err
		}
	}

	// 5. 检查用户状态
	if userInfo.Status == 0 {
		return nil, gerror.NewCode(CodeAuthAccountDisabled, "账号已被封禁，请联系管理员")
	}

	// 6. 生成JWT token
	token, err := s.generateToken(ctx, userInfo)
	if err != nil {
		return nil, err
	}

	return &model.WechatLoginOutput{
		Token:    token,
		UserInfo: userInfo,
	}, nil
}

// Logout 登出
func (s *sAuth) Logout(ctx context.Context, in *model.LogoutInput) (*model.LogoutOutput, error) {
	// 将token加入黑名单
	err := s.blacklistToken(ctx, in.Token)
	if err != nil {
		return &model.LogoutOutput{
			Success: false,
			Message: "登出失败: " + err.Error(),
		}, nil
	}

	return &model.LogoutOutput{
		Success: true,
		Message: "登出成功",
	}, nil
}

// ChangePassword 修改密码
func (s *sAuth) ChangePassword(ctx context.Context, in *model.ChangePasswordInput) (*model.ChangePasswordOutput, error) {
	// 1. 验证用户身份
	userInfo, err := service.User().GetUserByID(ctx, in.UserId)
	if err != nil {
		return nil, err
	}
	if userInfo == nil {
		return nil, gerror.NewCode(CodeAuthUserNotFound, "用户不存在")
	}

	// 2. 验证原密码
	if !s.verifyPassword(in.OldPassword, userInfo.Password) {
		return nil, gerror.NewCode(CodeAuthOldPasswordWrong, "原密码错误")
	}

	// 3. 验证新密码强度
	if len(in.NewPassword) < 6 {
		return nil, gerror.NewCode(CodeAuthPasswordWeak, "密码长度至少6位")
	}

	// 4. 更新密码
	err = service.User().ChangePassword(ctx, in.UserId, in.NewPassword)
	if err != nil {
		return &model.ChangePasswordOutput{
			Success: false,
			Message: "密码修改失败: " + err.Error(),
		}, nil
	}

	// 5. 将当前token加入黑名单，强制重新登录
	if in.Token != "" {
		_ = s.blacklistToken(ctx, in.Token)
	}

	return &model.ChangePasswordOutput{
		Success: true,
		Message: "密码修改成功，请重新登录",
	}, nil
}

// verifyPassword 验证密码
func (s *sAuth) verifyPassword(plainPassword, hashedPassword string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(plainPassword))
	return err == nil
}

// generateToken 生成JWT token
func (s *sAuth) generateToken(ctx context.Context, userInfo *model.UserInfo) (string, error) {
	// 获取JWT配置
	jwtSecret := g.Cfg().MustGet(ctx, "jwt.secret").String()
	jwtExpiration := g.Cfg().MustGet(ctx, "jwt.expiration").Int64()

	// 创建claims
	claims := jwt.MapClaims{
		"username": userInfo.Username,
		"role":     userInfo.Role,
		"userId":   userInfo.Id,
		"exp":      time.Now().Add(time.Duration(jwtExpiration) * time.Millisecond).Unix(),
		"iat":      time.Now().Unix(),
		"jti":      s.generateJTI(), // JWT ID，用于黑名单
	}

	// 创建token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(jwtSecret))
	if err != nil {
		return "", gerror.NewCode(CodeAuthTokenInvalid, "token生成失败")
	}

	return tokenString, nil
}

// generateJTI 生成JWT ID
func (s *sAuth) generateJTI() string {
	timestamp := gtime.Now().TimestampMilli()
	randomNum, _ := rand.Int(rand.Reader, big.NewInt(999999))
	return fmt.Sprintf("%d_%d", timestamp, randomNum.Int64())
}

// blacklistToken 将token加入黑名单
func (s *sAuth) blacklistToken(ctx context.Context, tokenString string) error {
	// 解析token获取JTI
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		jwtSecret := g.Cfg().MustGet(ctx, "jwt.secret").String()
		return []byte(jwtSecret), nil
	})

	if err != nil {
		return err
	}

	if claims, ok := token.Claims.(jwt.MapClaims); ok {
		if jti, exists := claims["jti"]; exists {
			// 将JTI存入Redis黑名单，过期时间与token一致
			exp := gconv.Int64(claims["exp"])
			ttl := time.Unix(exp, 0).Sub(time.Now())
			if ttl > 0 {
				_, err = g.Redis().Set(ctx, fmt.Sprintf("jwt:blacklist:jti:%v", jti), "1", ttl)
				return err
			}
		}
	}

	return nil
}

// getWechatOpenid 通过微信code获取openid
func (s *sAuth) getWechatOpenid(ctx context.Context, code string) (string, error) {
	// 获取微信配置
	appid := g.Cfg().MustGet(ctx, "wechat.appid").String()
	secret := g.Cfg().MustGet(ctx, "wechat.secret").String()
	loginUrl := g.Cfg().MustGet(ctx, "wechat.loginUrl").String()

	// 构建请求URL
	url := fmt.Sprintf("%s?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code",
		loginUrl, appid, secret, code)

	// 发起HTTP请求
	client := gclient.New()
	response, err := client.Get(ctx, url)
	if err != nil {
		return "", err
	}
	defer response.Close()

	// 解析响应
	var result struct {
		Openid     string `json:"openid"`
		SessionKey string `json:"session_key"`
		Errcode    int    `json:"errcode"`
		Errmsg     string `json:"errmsg"`
	}

	err = json.Unmarshal(response.ReadAll(), &result)
	if err != nil {
		return "", err
	}

	// 检查微信API返回的错误
	if result.Errcode != 0 {
		return "", fmt.Errorf("微信API错误: %s", result.Errmsg)
	}

	if result.Openid == "" {
		return "", fmt.Errorf("未获取到openid")
	}

	return result.Openid, nil
}

// createWechatUser 创建微信用户
func (s *sAuth) createWechatUser(ctx context.Context, openid string) (*model.UserInfo, error) {
	// 生成随机用户名
	username := fmt.Sprintf("wx_%s", s.generateRandomString(8))

	// 生成随机密码
	password := s.generateRandomString(16)

	// 创建用户输入参数
	createInput := &service.UserCreateInput{
		Username: username,
		Email:    fmt.Sprintf("%<EMAIL>", openid),
		Password: password,
		Role:     "USER",
		Status:   1,
		Openid:   openid,
	}

	// 调用用户服务创建用户
	userInfo, err := service.User().CreateUser(ctx, createInput)
	if err != nil {
		return nil, err
	}

	return userInfo, nil
}

// generateRandomString 生成随机字符串
func (s *sAuth) generateRandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	result := make([]byte, length)
	for i := range result {
		num, _ := rand.Int(rand.Reader, big.NewInt(int64(len(charset))))
		result[i] = charset[num.Int64()]
	}
	return string(result)
}
