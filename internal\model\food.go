package model

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// FoodCreateInput 食物创建输入参数
type FoodCreateInput struct {
	Name       string  `json:"name" v:"required" dc:"食物名称"`
	Measure    string  `json:"measure" dc:"份量描述"`
	Grams      float64 `json:"grams" dc:"克数"`
	Calories   float64 `json:"calories" dc:"卡路里"`
	Protein    float64 `json:"protein" dc:"蛋白质(g)"`
	Fat        float64 `json:"fat" dc:"脂肪(g)"`
	SatFat     float64 `json:"satFat" dc:"饱和脂肪(g)"`
	Fiber      string  `json:"fiber" dc:"纤维"`
	Carbs      float64 `json:"carbs" dc:"碳水化合物(g)"`
	CategoryId int     `json:"categoryId" dc:"分类ID"`
	ImageUrl   string  `json:"imageUrl" dc:"图片URL"`
}

// FoodCreateOutput 食物创建输出结果
type FoodCreateOutput struct {
	FoodInfo *FoodInfo `json:"foodInfo" dc:"食物信息"`
}

// FoodUpdateInput 食物更新输入参数
type FoodUpdateInput struct {
	Id         int     `json:"id" v:"required" dc:"食物ID"`
	Name       string  `json:"name" dc:"食物名称"`
	Measure    string  `json:"measure" dc:"份量描述"`
	Grams      float64 `json:"grams" dc:"克数"`
	Calories   float64 `json:"calories" dc:"卡路里"`
	Protein    float64 `json:"protein" dc:"蛋白质(g)"`
	Fat        float64 `json:"fat" dc:"脂肪(g)"`
	SatFat     float64 `json:"satFat" dc:"饱和脂肪(g)"`
	Fiber      string  `json:"fiber" dc:"纤维"`
	Carbs      float64 `json:"carbs" dc:"碳水化合物(g)"`
	CategoryId int     `json:"categoryId" dc:"分类ID"`
	ImageUrl   string  `json:"imageUrl" dc:"图片URL"`
}

// FoodUpdateOutput 食物更新输出结果
type FoodUpdateOutput struct {
	Success bool   `json:"success" dc:"是否成功"`
	Message string `json:"message" dc:"结果消息"`
}

// FoodQueryInput 食物查询输入参数
type FoodQueryInput struct {
	Page       int    `json:"page" v:"required|min:1" dc:"页码"`
	Size       int    `json:"size" v:"required|min:1|max:100" dc:"每页数量"`
	Keyword    string `json:"keyword" dc:"关键词搜索"`
	CategoryId *int   `json:"categoryId" dc:"分类ID筛选"`
}

// FoodListOutput 食物列表输出结果
type FoodListOutput struct {
	List  []*FoodInfo `json:"list" dc:"食物列表"`
	Total int64       `json:"total" dc:"总数"`
	Page  int         `json:"page" dc:"当前页"`
	Size  int         `json:"size" dc:"每页数量"`
}

// FoodImageUpdateInput 食物图片更新输入参数
type FoodImageUpdateInput struct {
	Id       int    `json:"id" v:"required" dc:"食物ID"`
	ImageUrl string `json:"imageUrl" v:"required" dc:"图片URL"`
}

// FoodImageUpdateOutput 食物图片更新输出结果
type FoodImageUpdateOutput struct {
	Success bool   `json:"success" dc:"是否成功"`
	Message string `json:"message" dc:"结果消息"`
}

// FoodBatchImportInput 食物批量导入输入参数
type FoodBatchImportInput struct {
	Foods []*FoodCreateInput `json:"foods" v:"required" dc:"食物列表"`
}

// FoodBatchImportOutput 食物批量导入输出结果
type FoodBatchImportOutput struct {
	Success      bool   `json:"success" dc:"是否成功"`
	Message      string `json:"message" dc:"结果消息"`
	SuccessCount int    `json:"successCount" dc:"成功导入数量"`
	FailCount    int    `json:"failCount" dc:"失败数量"`
}

// FoodCategoryCreateInput 食物分类创建输入参数
type FoodCategoryCreateInput struct {
	Name        string `json:"name" v:"required" dc:"分类名称"`
	Description string `json:"description" dc:"分类描述"`
	Color       string `json:"color" dc:"分类颜色"`
	SortOrder   int    `json:"sortOrder" dc:"排序顺序"`
}

// FoodCategoryCreateOutput 食物分类创建输出结果
type FoodCategoryCreateOutput struct {
	CategoryInfo *FoodCategoryInfo `json:"categoryInfo" dc:"分类信息"`
}

// FoodCategoryUpdateInput 食物分类更新输入参数
type FoodCategoryUpdateInput struct {
	Id          int    `json:"id" v:"required" dc:"分类ID"`
	Name        string `json:"name" dc:"分类名称"`
	Description string `json:"description" dc:"分类描述"`
	Color       string `json:"color" dc:"分类颜色"`
	SortOrder   int    `json:"sortOrder" dc:"排序顺序"`
}

// FoodCategoryUpdateOutput 食物分类更新输出结果
type FoodCategoryUpdateOutput struct {
	Success bool   `json:"success" dc:"是否成功"`
	Message string `json:"message" dc:"结果消息"`
}

// FoodCategoryQueryInput 食物分类查询输入参数
type FoodCategoryQueryInput struct {
	Page int `json:"page" v:"required|min:1" dc:"页码"`
	Size int `json:"size" v:"required|min:1|max:100" dc:"每页数量"`
}

// FoodCategoryListOutput 食物分类列表输出结果
type FoodCategoryListOutput struct {
	List  []*FoodCategoryInfo `json:"list" dc:"分类列表"`
	Total int64               `json:"total" dc:"总数"`
	Page  int                 `json:"page" dc:"当前页"`
	Size  int                 `json:"size" dc:"每页数量"`
}

// FoodInfo 食物信息结构体
type FoodInfo struct {
	Id           int         `json:"id" dc:"食物ID"`
	Name         string      `json:"name" dc:"食物名称"`
	Measure      string      `json:"measure" dc:"份量描述"`
	Grams        float64     `json:"grams" dc:"克数"`
	Calories     float64     `json:"calories" dc:"卡路里"`
	Protein      float64     `json:"protein" dc:"蛋白质(g)"`
	Fat          float64     `json:"fat" dc:"脂肪(g)"`
	SatFat       float64     `json:"satFat" dc:"饱和脂肪(g)"`
	Fiber        string      `json:"fiber" dc:"纤维"`
	Carbs        float64     `json:"carbs" dc:"碳水化合物(g)"`
	CategoryId   int         `json:"categoryId" dc:"分类ID"`
	Category     string      `json:"category" dc:"分类名称"`
	ImageUrl     string      `json:"imageUrl" dc:"图片URL"`
	CategoryInfo *FoodCategoryInfo `json:"categoryInfo,omitempty" dc:"分类信息"`
}

// FoodCategoryInfo 食物分类信息结构体
type FoodCategoryInfo struct {
	Id          int         `json:"id" dc:"分类ID"`
	Name        string      `json:"name" dc:"分类名称"`
	Description string      `json:"description" dc:"分类描述"`
	Color       string      `json:"color" dc:"分类颜色"`
	SortOrder   int         `json:"sortOrder" dc:"排序顺序"`
	FoodCount   int         `json:"foodCount" dc:"该分类下的食物数量"`
	CreatedAt   *gtime.Time `json:"createdAt" dc:"创建时间"`
	UpdatedAt   *gtime.Time `json:"updatedAt" dc:"更新时间"`
}
