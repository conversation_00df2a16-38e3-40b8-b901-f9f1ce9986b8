package errcode

import "github.com/gogf/gf/v2/errors/gcode"

// 系统级错误码定义
// 错误码格式：AABBBCCC
// AA: 服务标识 (00-系统级)
// BBB: 模块标识 (000-999)
// CCC: 具体错误 (000-999)

var (
	// 基础系统错误码 (00000-00099)
	CodeSuccess      = gcode.New(0, "success", nil)                    // 成功
	CodeUnknown      = gcode.New(1, "unknown error", nil)              // 未知错误
	CodeInternalError = gcode.New(2, "internal error", nil)            // 内部错误

	// HTTP状态码相关错误 (00400-00599)
	CodeInvalidParam = gcode.New(400, "invalid parameter", nil)        // 无效参数
	CodeUnauthorized = gcode.New(401, "unauthorized", nil)             // 未授权
	CodeForbidden    = gcode.New(403, "forbidden", nil)                // 禁止访问
	CodeNotFound     = gcode.New(404, "not found", nil)                // 资源不存在
	CodeMethodNotAllowed = gcode.New(405, "method not allowed", nil)   // 方法不允许
	CodeConflict     = gcode.New(409, "conflict", nil)                 // 冲突
	CodeTooManyRequests = gcode.New(429, "too many requests", nil)     // 请求过多
	CodeServerError  = gcode.New(500, "internal server error", nil)    // 服务器内部错误
	CodeBadGateway   = gcode.New(502, "bad gateway", nil)              // 网关错误
	CodeServiceUnavailable = gcode.New(503, "service unavailable", nil) // 服务不可用
	CodeGatewayTimeout = gcode.New(504, "gateway timeout", nil)        // 网关超时

	// 数据验证错误 (00100-00199)
	CodeValidationFailed = gcode.New(100, "validation failed", nil)    // 数据验证失败
	CodeRequiredField    = gcode.New(101, "required field missing", nil) // 必填字段缺失
	CodeInvalidFormat    = gcode.New(102, "invalid format", nil)       // 格式无效
	CodeOutOfRange       = gcode.New(103, "value out of range", nil)   // 值超出范围
	CodeDuplicateValue   = gcode.New(104, "duplicate value", nil)      // 重复值

	// 数据库操作错误 (00200-00299)
	CodeDatabaseError    = gcode.New(200, "database error", nil)       // 数据库错误
	CodeRecordNotFound   = gcode.New(201, "record not found", nil)     // 记录不存在
	CodeRecordExists     = gcode.New(202, "record already exists", nil) // 记录已存在
	CodeConstraintViolation = gcode.New(203, "constraint violation", nil) // 约束违反
	CodeTransactionFailed = gcode.New(204, "transaction failed", nil)  // 事务失败

	// 网络和外部服务错误 (00300-00399)
	CodeNetworkError     = gcode.New(300, "network error", nil)        // 网络错误
	CodeTimeoutError     = gcode.New(301, "timeout error", nil)        // 超时错误
	CodeExternalServiceError = gcode.New(302, "external service error", nil) // 外部服务错误
	CodeRateLimitExceeded = gcode.New(303, "rate limit exceeded", nil) // 速率限制超出

	// 文件操作错误 (00400-00499)
	CodeFileError        = gcode.New(400, "file error", nil)           // 文件错误
	CodeFileNotFound     = gcode.New(401, "file not found", nil)       // 文件不存在
	CodeFileTooBig       = gcode.New(402, "file too big", nil)         // 文件过大
	CodeInvalidFileType  = gcode.New(403, "invalid file type", nil)    // 无效文件类型
	CodeFileUploadFailed = gcode.New(404, "file upload failed", nil)   // 文件上传失败

	// 缓存操作错误 (00500-00599)
	CodeCacheError       = gcode.New(500, "cache error", nil)          // 缓存错误
	CodeCacheKeyNotFound = gcode.New(501, "cache key not found", nil)  // 缓存键不存在
	CodeCacheExpired     = gcode.New(502, "cache expired", nil)        // 缓存过期
)
