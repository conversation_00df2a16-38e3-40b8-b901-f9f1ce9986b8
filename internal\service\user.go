package service

import (
	"context"
	"shikeyinxiang-goframe/internal/model"
)

// 临时用户服务接口定义，用于认证模块调用
// 完整的用户服务接口将在后续任务中实现

type IUser interface {
	// GetUserByEmail 根据邮箱获取用户
	GetUserByEmail(ctx context.Context, email string) (*model.UserInfo, error)
	
	// GetUserByUsername 根据用户名获取用户
	GetUserByUsername(ctx context.Context, username string) (*model.UserInfo, error)
	
	// GetUserByOpenid 根据微信openid获取用户
	GetUserByOpenid(ctx context.Context, openid string) (*model.UserInfo, error)
	
	// GetUserByID 根据用户ID获取用户
	GetUserByID(ctx context.Context, userId int64) (*model.UserInfo, error)
	
	// CreateUser 创建用户
	CreateUser(ctx context.Context, in *model.UserCreateInput) (*model.UserInfo, error)
	
	// ChangePassword 修改密码
	ChangePassword(ctx context.Context, userId int64, newPassword string) error
}

// UserCreateInput 临时用户创建输入参数
// 完整定义将在用户模块任务中实现
type UserCreateInput struct {
	Username string `json:"username"`
	Email    string `json:"email"`
	Password string `json:"password"`
	Role     string `json:"role"`
	Status   int    `json:"status"`
	Openid   string `json:"openid"`
}

var localUser IUser

func User() IUser {
	if localUser == nil {
		panic("implement not found for interface IUser, forgot register?")
	}
	return localUser
}

func RegisterUser(i IUser) {
	localUser = i
}
