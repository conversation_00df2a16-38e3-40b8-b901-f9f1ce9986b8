// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// DietRecords is the golang structure for table diet_records.
type DietRecords struct {
	Id           int64       `json:"id"           orm:"id"            description:""`                                    //
	UserId       int64       `json:"userId"       orm:"user_id"       description:"用户ID"`                                // 用户ID
	Date         *gtime.Time `json:"date"         orm:"date"          description:"记录日期"`                                // 记录日期
	Time         *gtime.Time `json:"time"         orm:"time"          description:"记录时间"`                                // 记录时间
	MealType     string      `json:"mealType"     orm:"meal_type"     description:"餐次类型: breakfast/lunch/dinner/snacks"` // 餐次类型: breakfast/lunch/dinner/snacks
	Remark       string      `json:"remark"       orm:"remark"        description:"备注信息"`                                // 备注信息
	TotalCalorie float64     `json:"totalCalorie" orm:"total_calorie" description:"总热量(千卡)"`                             // 总热量(千卡)
	CreatedAt    *gtime.Time `json:"createdAt"    orm:"created_at"    description:"创建时间"`                                // 创建时间
	UpdatedAt    *gtime.Time `json:"updatedAt"    orm:"updated_at"    description:"更新时间"`                                // 更新时间
}
